# ==================== 实例基本信息 ====================
output "instance_id" {
  description = "实例 ID"
  value       = tencentcloud_instance.coder_instance.id
}

output "instance_name" {
  description = "实例名称"
  value       = tencentcloud_instance.coder_instance.instance_name
}

output "instance_type" {
  description = "实例规格"
  value       = tencentcloud_instance.coder_instance.instance_type
}

# ==================== 网络连接信息 ====================
output "public_ip" {
  description = "公有 IP"
  value       = tencentcloud_instance.coder_instance.public_ip
}

output "private_ip" {
  description = "私有 IP"
  value       = tencentcloud_instance.coder_instance.private_ip
}

# ==================== Coder Agent 连接信息 ====================
output "coder_agent_token" {
  description = "Coder Agent 连接令牌"
  value       = coder_agent.main.token
  sensitive   = true
}

output "coder_agent_init_script" {
  description = "Coder Agent 初始化脚本"
  value       = coder_agent.main.init_script
  sensitive   = true
}

# ==================== SSH 连接信息 ====================
output "ssh_connection" {
  description = "使用 SSH 连接"
  value       = "ssh -i ~/.ssh/coder-key.pem root@${tencentcloud_instance.coder_instance.public_ip}"
}

output "ssh_config" {
  description = "SSH 配置信息"
  value = {
    host     = tencentcloud_instance.coder_instance.public_ip
    port     = 22
    username = "root"  # CVM 实例默认用户
    key_file = "~/.ssh/coder-key.pem"
  }
}

# ==================== JuiceFS 工作空间信息 ====================
output "juicefs_mount_point" {
  description = "JuiceFS 挂载点路径"
  value       = "/data/workspaces"
}

output "shared_workspace_path" {
  description = "共享工作空间路径"
  value       = "/data/workspaces"
}

output "workspace_shortcut" {
  description = "工作空间快捷访问路径"
  value       = "/root/workspace"
}

# ==================== JetBrains Gateway 信息 ====================
output "jetbrains_gateway_info" {
  description = "JetBrains Gateway 配置信息"
  value = {
    supported_ides = ["WebStorm", "RustRover", "PyCharm Professional", "CLion"]
    default_ide    = "WebStorm"
    folder         = "/data/workspaces"
    latest_version = true
    usage_note     = "点击 Web UI 中的 JetBrains Gateway 按钮选择 IDE 并自动下载"
  }
}

# ==================== 开发工具版本信息 ====================
output "development_tools" {
  description = "预安装的开发工具版本信息"
  value = {
    note = "以下工具版本在工作空间启动后可用，具体版本取决于基础镜像"
    tools = {
      nodejs = "通过 nvm 管理，支持多版本"
      python = "Python 3.x (系统默认版本)"
      rust   = "通过 rustup 管理，支持最新稳定版"
      git    = "系统包管理器安装的版本"
      docker = "Docker Engine (如果启用)"
    }
    check_command = "在工作空间中运行以下命令查看具体版本："
    version_commands = {
      nodejs = "node --version"
      python = "python3 --version"
      rust   = "rustc --version"
      git    = "git --version"
      docker = "docker --version"
    }
  }
}

# ==================== 配置摘要 ====================
output "configuration_summary" {
  description = "实例配置摘要"
  value = {
    region            = local.region
    availability_zone = local.availability_zone
    instance_type     = data.coder_parameter.instance_type.value
    image_id          = local.image_id
    charge_type       = data.coder_parameter.instance_charge_type.value
    system_disk_type  = local.system_disk_type
    system_disk_size  = local.system_disk_size
    vpc_id           = local.vpc_id
    subnet_id        = local.subnet_id
    security_group_id = local.security_group_id
    juicefs_filesystem = var.juicefs_vol_name
    juicefs_mount_point = "/data/workspaces"
    shared_workspace = "/data/workspaces"
    jetbrains_gateway = "已配置，支持 WebStorm、RustRover、PyCharm、CLion"
  }
}
