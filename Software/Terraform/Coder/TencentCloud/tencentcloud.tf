terraform {
  required_providers {
    tencentcloud = {
      source  = "tencentcloudstack/tencentcloud"
      version = "1.82.24"
    }
    coder = {
      source  = "coder/coder"
      version = "2.11.0"
    }
  }
}

# 配置腾讯云 Provider
provider "tencentcloud" {
  secret_id  = var.tencentcloud_cvm_secret_id
  secret_key = var.tencentcloud_cvm_secret_key
  region     = local.region
}

# 配置 Coder Provider
provider "coder" {}

# 配置 Coder Agent - 支持远程开发工具连接
resource "coder_agent" "main" {
  arch = "amd64"  # 目标架构
  os   = "linux"  # 目标操作系统
  startup_script_behavior = "blocking"  # 阻塞模式: 启动脚本必须完成后工作空间才可用, 确保环境完全初始化.
  dir = "/data/workspaces"  # 用户连接时的默认工作目录, 直接进入 JuiceFS 挂载的共享工作空间.
  connection_timeout = 300  # Agent 连接超时时间 (秒) 给予充足时间完成 JuiceFS 挂载和环境初始化

  # 配置 Coder Web UI 中显示的基础工具
  display_apps {
    vscode                  = false  # 禁用 VSCode: 专注于 JetBrains 开发体验
    vscode_insiders         = false  # 禁用 VSCode Insiders
    web_terminal            = true  # 禁用 Web 终端: 简化界面专注于 IDE 集成
    ssh_helper              = true   # 启用 SSH 助手: 提供故障排查和直接连接支持
    port_forwarding_helper  = true   # 启用端口转发助手: 便于调试 Web 服务和数据库连接
  }

  # 内联脚本解决方案 - 直接嵌入脚本内容，解决文件传输问题
  startup_script = <<-EOT
    #!/bin/bash
    set -e

    # 设置非交互式环境
    export DEBIAN_FRONTEND=noninteractive

    echo "🚀 开始初始化 Coder 工作空间..."

    # 第一阶段: 系统环境配置 - 内联脚本内容
    echo "🔧 第一阶段: 配置系统开发环境..."

    # === 直接嵌入 system_setup.sh 的内容 ===
    ${indent(4, file("${path.module}/scripts/system_setup.sh"))}

    echo "✅ 系统环境配置完成!"

    # 第二阶段: JuiceFS 配置 - 内联脚本内容
    echo "💾 第二阶段: 配置 JuiceFS 云服务..."

    # 设置JuiceFS参数为环境变量
    export FILESYSTEM_NAME="${var.juicefs_vol_name}"
    export JUICEFS_TOKEN="${var.juicefs_token}"
    export COS_ACCESS_KEY="${var.tencentcloud_cos_secret_id}"
    export COS_SECRET_KEY="${var.tencentcloud_cos_secret_key}"
    export COS_ENDPOINT="${var.tencentcloud_cos_endpoint}"

    # === 直接嵌入 juicefs_setup.sh 的内容（修改为环境变量版本）===
    ${indent(4, replace(replace(file("${path.module}/scripts/juicefs_setup.sh"),
      "FILESYSTEM_NAME=\"$1\"", "# FILESYSTEM_NAME already set as environment variable"),
      "JUICEFS_TOKEN=\"$2\"", "# JUICEFS_TOKEN already set as environment variable"))}

    # 显示最终系统信息
    echo ""
    echo "系统信息总览:"
    CPU_COUNT=$(nproc)
    # 获取内存信息（统一使用GB单位）
    MEM_TOTAL_MB=$(free -m | grep '^Mem:' | awk '{print $2}')
    MEM_TOTAL_GB=$((MEM_TOTAL_MB * 10 / 1024))
    MEM_DISPLAY=$((MEM_TOTAL_GB / 10)).$((MEM_TOTAL_GB % 10))
    DISK_INFO=$(df -h / | tail -1 | awk '{print $2}')
    echo "   CPU: $${CPU_COUNT} 核心"
    echo "   内存: $${MEM_DISPLAY}GB"
    echo "   系统盘: $${DISK_INFO}"
    echo ""
    echo "Coder 工作空间初始化完成!"
    echo "开发环境已就绪, 开始您的编程之旅吧!"

    # 注册 JetBrains Gateway 后端 (支持多个IDE)
    echo "注册 JetBrains Gateway 后端..."
    JETBRAINS_DIR="/home/<USER>/JetBrains/backends"
    REGISTERED_COUNT=0

    # 调试信息：检查目录结构
    echo "调试信息："
    echo "- 检查 JetBrains 目录: $JETBRAINS_DIR"
    if [ -d "$JETBRAINS_DIR" ]; then
      echo "- 目录存在，内容："
      ls -la "$JETBRAINS_DIR" || echo "- 无法列出目录内容"
    else
      echo "- 目录不存在"
    fi

    # 注册 WebStorm
    if [ -d "$JETBRAINS_DIR/WS" ]; then
        WS_BACKEND=$(find "$JETBRAINS_DIR/WS" -name "webstorm-*" -type d | head -1)
        if [ -n "$WS_BACKEND" ] && [ -f "$WS_BACKEND/bin/remote-dev-server.sh" ]; then
            sudo -u coder "$WS_BACKEND/bin/remote-dev-server.sh" registerBackendLocationForGateway || echo "⚠️ WebStorm 后端注册失败"
            echo "✅ WebStorm 后端注册完成"
            REGISTERED_COUNT=$((REGISTERED_COUNT + 1))
        fi
    fi

    # 注册 RustRover
    if [ -d "$JETBRAINS_DIR/RR" ]; then
        RR_BACKEND=$(find "$JETBRAINS_DIR/RR" -name "rustrover-*" -type d | head -1)
        if [ -n "$RR_BACKEND" ] && [ -f "$RR_BACKEND/bin/remote-dev-server.sh" ]; then
            sudo -u coder "$RR_BACKEND/bin/remote-dev-server.sh" registerBackendLocationForGateway || echo "⚠️ RustRover 后端注册失败"
            echo "✅ RustRover 后端注册完成"
            REGISTERED_COUNT=$((REGISTERED_COUNT + 1))
        fi
    fi

    # 注册 PyCharm
    if [ -d "$JETBRAINS_DIR/PY" ]; then
        PY_BACKEND=$(find "$JETBRAINS_DIR/PY" -name "pycharm-*" -type d | head -1)
        if [ -n "$PY_BACKEND" ] && [ -f "$PY_BACKEND/bin/remote-dev-server.sh" ]; then
            sudo -u coder "$PY_BACKEND/bin/remote-dev-server.sh" registerBackendLocationForGateway || echo "⚠️ PyCharm 后端注册失败"
            echo "✅ PyCharm 后端注册完成"
            REGISTERED_COUNT=$((REGISTERED_COUNT + 1))
        fi
    fi

    # 注册 CLion
    if [ -d "$JETBRAINS_DIR/CL" ]; then
        CL_BACKEND=$(find "$JETBRAINS_DIR/CL" -name "clion-*" -type d | head -1)
        if [ -n "$CL_BACKEND" ] && [ -f "$CL_BACKEND/bin/remote-dev-server.sh" ]; then
            sudo -u coder "$CL_BACKEND/bin/remote-dev-server.sh" registerBackendLocationForGateway || echo "⚠️ CLion 后端注册失败"
            echo "✅ CLion 后端注册完成"
            REGISTERED_COUNT=$((REGISTERED_COUNT + 1))
        fi
    fi

    if [ $REGISTERED_COUNT -gt 0 ]; then
        echo "已注册 $REGISTERED_COUNT 个 JetBrains IDE 后端"
    else
        echo "未找到已安装的 IDE 后端"
        echo "说明：IDE 后端将在首次点击 Gateway 按钮时自动下载"
        echo "这是正常的，因为 Coder 使用按需下载机制"
    fi

    # 确保 coder 用户拥有工作空间目录
    chown -R coder:coder /data/workspaces 2>/dev/null || true

    # 显示 Coder Agent 连接信息
    echo ""
    echo "Coder Agent 连接调试："
    echo "- Agent Token: $(echo $CODER_AGENT_TOKEN | cut -c1-12)..."
    echo "- 工作目录: /data/workspaces"
    echo "- Agent 监听地址: 127.0.0.1:$(echo $CODER_AGENT_TOKEN | tail -c 5)"
    echo "- 支持的 IDE: WebStorm, RustRover, PyCharm, CLion"

    # 检查网络连接
    echo ""
    echo "网络连接检查："
    if curl -s --connect-timeout 5 https://coder.lilh.net/healthz >/dev/null; then
        echo "- Coder 服务器连接: 正常"
    else
        echo "- Coder 服务器连接: 失败"
    fi

    # 确保脚本执行成功
    echo "启动脚本执行完成"
  EOT

  # 环境变量配置
  env = {
    WORKSPACE_DIR = "/data/workspaces"
    JUICEFS_MOUNT = "/data/workspaces"
  }

  # 元数据配置 - 关键系统和开发环境监控
  metadata {
    display_name = "JuiceFS 挂载状态"
    key          = "juicefs_status"
    script       = "mountpoint -q /data/workspaces && echo '已挂载' || echo '未挂载'"
    interval     = 30
    timeout      = 5
  }

  metadata {
    display_name = "CPU 使用率"
    key          = "cpu_usage"
    script       = <<-EOT
      #!/bin/bash
      # 获取 CPU 使用率
      if command -v top >/dev/null 2>&1; then
        CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
        if [ -n "$CPU_USAGE" ] && [ "$CPU_USAGE" != "" ]; then
          echo "$${CPU_USAGE}%"
        else
          echo "0.0%"
        fi
      else
        echo "0.0%"
      fi
    EOT
    interval     = 30
    timeout      = 10
  }

  metadata {
    display_name = "内存使用率"
    key          = "memory_usage"
    script       = <<-EOT
      #!/bin/bash
      # 获取内存使用率
      if command -v free >/dev/null 2>&1; then
        # 获取内存信息（以MB为单位）
        MEM_LINE=$(free -m | grep '^Mem:')
        if [ -n "$MEM_LINE" ]; then
          TOTAL=$(echo "$MEM_LINE" | awk '{print $2}')
          USED=$(echo "$MEM_LINE" | awk '{print $3}')

          if [ "$TOTAL" -gt 0 ] 2>/dev/null; then
            PERCENT=$((USED * 100 / TOTAL))

            # 根据大小选择合适的单位显示
            if [ "$TOTAL" -ge 1024 ]; then
              # 大于等于1GB时显示GB
              TOTAL_GB=$((TOTAL * 10 / 1024))
              USED_GB=$((USED * 10 / 1024))
              TOTAL_DISPLAY=$((TOTAL_GB / 10)).$((TOTAL_GB % 10))
              USED_DISPLAY=$((USED_GB / 10)).$((USED_GB % 10))
              echo "$${USED_DISPLAY}GB/$${TOTAL_DISPLAY}GB ($${PERCENT}%)"
            else
              # 小于1GB时显示MB
              echo "$${USED}MB/$${TOTAL}MB ($${PERCENT}%)"
            fi
          else
            echo "N/A"
          fi
        else
          echo "N/A"
        fi
      else
        echo "N/A"
      fi
    EOT
    interval     = 30
    timeout      = 10
  }

  metadata {
    display_name = "JetBrains Gateway 状态"
    key          = "jetbrains_gateway_status"
    script       = <<-EOT
      # 检查是否有已下载的 IDE 后端
      BACKENDS_DIR="/home/<USER>/JetBrains/backends"
      if [ -d "$BACKENDS_DIR" ] && [ "$(ls -A $BACKENDS_DIR 2>/dev/null)" ]; then
        INSTALLED_COUNT=0
        [ -d "$BACKENDS_DIR/WS" ] && INSTALLED_COUNT=$((INSTALLED_COUNT + 1))
        [ -d "$BACKENDS_DIR/RR" ] && INSTALLED_COUNT=$((INSTALLED_COUNT + 1))
        [ -d "$BACKENDS_DIR/PY" ] && INSTALLED_COUNT=$((INSTALLED_COUNT + 1))
        [ -d "$BACKENDS_DIR/CL" ] && INSTALLED_COUNT=$((INSTALLED_COUNT + 1))
        if [ $INSTALLED_COUNT -gt 0 ]; then
          echo "已安装 $${INSTALLED_COUNT} 个IDE"
        else
          echo "未安装"
        fi
      else
        echo "未安装"
      fi
    EOT
    interval     = 30
    timeout      = 5
  }

  metadata {
    display_name = "Coder Agent 状态"
    key          = "coder_agent_status"
    script       = <<-EOT
      # 检查 Coder Agent 进程状态
      if pgrep -f "coder.*agent" >/dev/null 2>&1; then
        echo "运行中"
      else
        echo "未运行"
      fi
    EOT
    interval     = 30
    timeout      = 5
  }

  metadata {
    display_name = "JetBrains 后端进程状态"
    key          = "jetbrains_processes"
    script       = <<-EOT
      # 检查 JetBrains 相关进程
      PIDS=$(pgrep -f "remote-dev-server" 2>/dev/null)
      if [ -n "$PIDS" ]; then
        echo "$PIDS 运行中"
      else
        echo "无进程"
      fi
    EOT
    interval     = 15
    timeout      = 8
  }


}

# ==================== JetBrains Gateway 远程开发集成 ====================
#
# 使用官方 JetBrains Gateway 模块提供一键启动入口
# 该模块创建 Gateway 按钮，用户可以选择不同的 IDE
# Gateway 架构：前端客户端(本地) + 后端IDE(远程工作空间)
#

# 暂时禁用官方模块，使用手动配置
# module "jetbrains_gateway" {
#   count          = data.coder_workspace.me.start_count
#   source         = "github.com/coder/modules//jetbrains-gateway"
#   agent_id       = coder_agent.main.id
#   folder         = "/data/workspaces"
#   jetbrains_ides = ["WS", "RR", "PY", "CL"]
#   default        = "RR"
#   latest         = true
# }

# ==================== 快捷应用链接 ====================
# 使用 Coder 原生 SSH 显示，无需自定义按钮

# 手动创建 JetBrains Gateway 应用
resource "coder_app" "jetbrains_gateway" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "jetbrains-gateway"
  display_name = "JetBrains Gateway"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/Gateway_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}"
  external     = true
}

# RustRover 专用按钮
resource "coder_app" "rustrover" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "rustrover"
  display_name = "RustRover"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/RustRover_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}&ide_product_code=RR"
  external     = true
}

# 注意：JetBrains Gateway 按钮由上面的官方模块自动创建

# ==================== JetBrains Gateway 使用指南 ====================
#
# 本地环境准备:
# 1. 下载安装 JetBrains Gateway: https://www.jetbrains.com/remote-development/gateway/
# 2. 在 Gateway 中安装 Coder 插件
# 3. 配置 Coder 服务器地址: https://coder.lilh.net
#
# 使用方式:
# 1. 在 Coder Web UI 中点击 JetBrains Gateway 按钮
# 2. 选择需要的 IDE (WebStorm/RustRover/PyCharm/CLion)
# 3. Gateway 会自动下载并启动选定的 IDE 后端
# 4. 首次使用时需要等待 IDE 下载完成 (约 3-5 分钟)
#
# 支持的 IDE (按需下载):
# - WebStorm: JavaScript/TypeScript 开发
# - RustRover: Rust 开发
# - PyCharm Professional: Python 开发
# - CLion: C/C++ 开发
#
# 架构优势:
# - 前端UI在本地，响应快速
# - 后端计算在远程，资源充足
# - 按需下载，节省存储空间
# - 接近本地IDE的开发体验

# 创建 CVM 实例
resource "tencentcloud_instance" "coder_instance" {
  # 基本配置
  instance_name     = "coder-${data.coder_workspace_owner.me.name}-${data.coder_workspace.me.name}"
  availability_zone = local.availability_zone
  image_id          = local.image_id
  instance_type     = data.coder_parameter.instance_type.value
  hostname          = "coder-${substr(data.coder_workspace.me.id, 0, 8)}"
  project_id        = local.project_id

  # 计费配置
  instance_charge_type = data.coder_parameter.instance_charge_type.value

  # 竞价实例配置 (选择竞价实例时使用用户自定义价格)
  spot_instance_type = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? "ONE-TIME" : null
  spot_max_price     = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? data.coder_parameter.spot_max_price.value : null

  # 网络配置 (使用固定配置)
  vpc_id                     = local.vpc_id
  subnet_id                  = local.subnet_id
  orderly_security_groups    = [local.security_group_id]
  allocate_public_ip         = local.allocate_public_ip
  internet_charge_type       = local.internet_charge_type
  internet_max_bandwidth_out = local.internet_max_bandwidth_out

  # 存储配置 (使用固定配置)
  system_disk_type = local.system_disk_type
  system_disk_size = local.system_disk_size

  # SSH 密钥认证 (使用固定配置)
  key_ids = [local.ssh_key_id]

  # 用户数据脚本 - 安装 Coder Agent
  user_data = base64encode(templatefile("${path.module}/scripts/user_data.sh", {
    init_script = base64encode(coder_agent.main.init_script)
    coder_agent_token = coder_agent.main.token
  }))

  # 服务配置 (按要求禁用相关服务)
  disable_security_service   = true  # 不启用安全加固
  disable_monitor_service    = true  # 不启用云监控
  disable_automation_service = true  # 不启用自动化助手

  # 标签配置
  tags = {
    "Coder"        = "true"
    "Owner"        = data.coder_workspace_owner.me.name
    "Workspace"    = data.coder_workspace.me.name
    "InstanceType" = data.coder_parameter.instance_type.value
    "ChargeType"   = data.coder_parameter.instance_charge_type.value
    "Environment"  = "Development"
    "CreatedBy"    = "Terraform"
  }
}