# JetBrains Gateway 连接问题故障排除指南

## 🔧 最新修复 (2025-09-20)

### 关键问题发现
**模板导入卡住的根本原因**:
1. 官方 `coder/modules` 仓库已被归档，模块迁移到新的 registry 地址
2. **参数冲突**: `version = "1.2.2"` 与 `latest = true` 参数冲突导致 Terraform 验证失败

### 修复内容
1. **修复模块引用地址**: 从 `github.com/coder/modules//jetbrains-gateway` 更新为 `registry.coder.com/coder/jetbrains-gateway/coder`
2. **修复参数冲突**: 移除 `latest = true` 参数，避免与 `version = "1.2.2"` 冲突
3. **简化模块配置**: 只保留必要参数，移除可能有问题的 `jetbrains_ides` 和 `default` 参数
4. **修复用户权限问题**: 统一使用 root 用户，移除 coder 用户相关配置
5. **移除冲突的手动注册代码**: 清理 startup_script 中的手动 JetBrains 后端注册逻辑
6. **更新监控脚本**: 修正 JetBrains 后端目录路径为 root 用户目录

### 关键变更
- 修正模块源地址：`source = "registry.coder.com/coder/jetbrains-gateway/coder"`
- 添加版本锁定：`version = "1.2.2"`
- **移除冲突参数**：删除 `latest = true`、`jetbrains_ides`、`default` 参数
- 移除手动创建的 `coder_app` 资源
- 修正目录权限为 `root:root`
- 更新监控脚本路径为 `/root/.cache/JetBrains/RemoteDev/dist`

## 🚀 使用流程

### 1. 工作空间创建后的验证步骤

#### 检查 Coder Agent 状态
```bash
# SSH 连接到工作空间
ssh -i ~/.ssh/your-key.pem root@<工作空间IP>

# 检查 Coder Agent 进程
ps aux | grep coder
```

#### 检查 JetBrains Gateway 模块状态
在 Coder Web UI 中查看工作空间的 "Apps" 部分，应该能看到：
- JetBrains Gateway 按钮（由官方模块自动创建）
- 支持的 IDE: WebStorm, RustRover, PyCharm, CLion

#### 检查元数据监控
在工作空间详情页面查看以下监控指标：
- **JuiceFS 挂载状态**: 应显示 "已挂载"
- **JetBrains Gateway 状态**: 初始显示 "未安装"（正常，按需下载）
- **Coder Agent 状态**: 应显示 "运行中"

### 2. JetBrains Gateway 连接流程

#### 方法一：使用 Coder Web UI（推荐）
1. 在工作空间页面点击 "JetBrains Gateway" 按钮
2. 选择需要的 IDE（WebStorm/RustRover/PyCharm/CLion）
3. Gateway 会自动下载并启动选定的 IDE 后端
4. 首次使用需要等待 3-5 分钟下载完成

#### 方法二：使用 JetBrains Gateway 客户端
1. 安装 JetBrains Gateway: https://www.jetbrains.com/remote-development/gateway/
2. 安装 Coder 插件: https://plugins.jetbrains.com/plugin/19620-coder
3. 配置 Coder 服务器地址: `https://coder.lilh.net`
4. 选择工作空间和 IDE，开始连接

### 3. 故障排除步骤

#### 问题：模板导入卡在 "unpacking template source archive"
**可能原因**:
1. Terraform 配置语法错误
2. 模块参数冲突
3. 模块版本不存在

**解决方案**:
```bash
# 1. 检查参数冲突
grep -E "(latest.*true|version.*=)" tencentcloud.tf

# 2. 验证 Terraform 语法
terraform init -backend=false
terraform validate

# 3. 使用备用方案
mv jetbrains_manual_config.tf.backup jetbrains_manual_config.tf
# 然后注释掉官方模块配置
```

#### 问题：Gateway 按钮不显示
**可能原因**: 官方模块未正确加载
**解决方案**:
```bash
# 检查 Terraform 状态
terraform plan
terraform apply

# 查看模块输出
terraform output
```

#### 问题：点击 Gateway 按钮无响应
**可能原因**: 
1. 本地未安装 JetBrains Gateway
2. Gateway URL 格式错误

**解决方案**:
1. 确保本地安装了 JetBrains Gateway
2. 检查浏览器是否允许打开 `jetbrains-gateway://` 协议

#### 问题：IDE 后端下载失败
**可能原因**: 网络连接问题或权限问题
**解决方案**:
```bash
# 检查网络连接
curl -I https://download.jetbrains.com/

# 检查磁盘空间
df -h

# 检查下载目录权限
ls -la /root/.cache/JetBrains/
```

#### 问题：连接超时
**可能原因**: 
1. 安全组端口未开放
2. Agent 连接问题

**解决方案**:
```bash
# 检查 Agent 连接
curl -s http://127.0.0.1:$CODER_AGENT_PORT/api/v2/buildinfo

# 检查网络连接
netstat -tlnp | grep coder

# 检查防火墙状态
ufw status
```

### 4. 调试命令集合

#### 系统状态检查
```bash
# 检查系统资源
free -h
df -h
top

# 检查网络连接
ping coder.lilh.net
nslookup coder.lilh.net
```

#### JetBrains 相关检查
```bash
# 检查 JetBrains 目录
ls -la /root/.cache/JetBrains/
ls -la /root/.cache/JetBrains/RemoteDev/

# 检查已下载的 IDE
ls -la /root/.cache/JetBrains/RemoteDev/dist/

# 检查 JetBrains 进程
ps aux | grep -i jetbrains
ps aux | grep remote-dev
```

#### 日志检查
```bash
# Coder Agent 日志
journalctl -u coder-agent -f

# 系统日志
tail -f /var/log/syslog

# 启动脚本日志
cat /tmp/coder-startup-script.log
```

## 📋 常见问题 FAQ

### Q: 为什么 JetBrains Gateway 状态显示"未安装"？
A: 这是正常的。Coder 使用按需下载机制，IDE 后端会在首次点击 Gateway 按钮时自动下载。

### Q: 首次连接为什么这么慢？
A: 首次连接需要下载 IDE 后端（约 500MB-1GB），根据网络速度需要 3-5 分钟。后续连接会很快。

### Q: 支持哪些 JetBrains IDE？
A: 当前配置支持：
- WebStorm (WS): JavaScript/TypeScript 开发
- RustRover (RR): Rust 开发  
- PyCharm Professional (PY): Python 开发
- CLion (CL): C/C++ 开发

### Q: 如何添加更多 IDE 支持？
A: 修改 `tencentcloud.tf` 中的 `jetbrains_ides` 参数，添加对应的 IDE 代码。

### Q: 工作空间重启后需要重新下载 IDE 吗？
A: 不需要。IDE 后端下载到 `/root/.cache` 目录，会持久保存。

## 🔗 相关链接

- [JetBrains Gateway 官方文档](https://www.jetbrains.com/help/idea/remote-development-a.html)
- [Coder JetBrains Gateway 模块](https://github.com/coder/modules/tree/main/jetbrains-gateway)
- [Coder 官方文档](https://coder.com/docs)
