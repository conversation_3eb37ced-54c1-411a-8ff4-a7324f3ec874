# Coder 腾讯云 + JuiceFS 模板项目结构

## 📁 目录结构

```
TencentCloud/
├── README.md                    # 项目主要说明文档
├── tencentcloud.tf             # Terraform 主配置文件
├── variables.tf                # 参数定义文件
├── outputs.tf                  # 输出配置文件
├── scripts/                    # 脚本文件目录
│   ├── user_data.sh           # Coder Agent 安装脚本
│   ├── system_setup.sh        # 系统环境配置脚本
│   └── juicefs_setup.sh       # JuiceFS 配置脚本
└── docs/                       # 文档目录
    ├── PROJECT_STRUCTURE.md   # 项目结构说明（本文件）
    └── terraform.tfvars.example # 配置示例文件
```

## 📋 文件说明

### **核心 Terraform 文件**

#### `tencentcloud.tf`
- **作用**: Terraform 主配置文件
- **内容**: Provider 配置、资源定义、Coder Agent 配置
- **职责**: 协调整个基础设施的创建和配置

#### `variables.tf`
- **作用**: 定义所有用户可配置的参数
- **内容**: CVM 配置参数、JuiceFS 参数、COS 参数
- **特点**: 包含参数验证和默认值

#### `outputs.tf`
- **作用**: 定义模板的输出信息
- **内容**: 连接信息、工作空间路径、配置摘要
- **用途**: 为用户提供必要的连接和使用信息

### **脚本文件目录 (`scripts/`)**

#### `user_data.sh`
- **执行时机**: CVM 实例首次启动时
- **执行用户**: root
- **主要职责**: 
  - 安装 Coder Agent 运行的基础依赖
  - 启动 Coder Agent 服务
- **设计原则**: 最小化依赖，快速启动

#### `system_setup.sh`
- **执行时机**: Coder Agent 启动后（startup_script 第一阶段）
- **执行用户**: root
- **主要职责**:
  - 更新系统包管理器
  - 安装开发基础工具（git、build-essential 等）
  - 安装网络和调试工具
  - 配置开发环境（可选组件）
- **扩展性**: 支持 Rust、Node.js、Python、Docker 等环境

#### `juicefs_setup.sh`
- **执行时机**: 系统环境配置完成后（startup_script 第二阶段）
- **执行用户**: root
- **主要职责**:
  - 安装 JuiceFS 客户端
  - 配置 JuiceFS 认证
  - 挂载 JuiceFS 文件系统
  - 创建工作空间快捷访问
- **参数**: 接收 5 个参数（文件系统名、令牌、COS 密钥等）

### **文档目录 (`docs/`)**

#### `terraform.tfvars.example`
- **作用**: 配置示例和说明文档
- **内容**: 详细的参数说明、使用指南、最佳实践
- **用途**: 帮助用户理解和配置模板参数

#### `PROJECT_STRUCTURE.md`
- **作用**: 项目结构说明文档（本文件）
- **内容**: 目录结构、文件职责、设计原则
- **用途**: 帮助开发者理解项目架构

## 🏗️ 设计原则

### **1. 关注点分离**
- **系统配置** vs **存储配置**: 分别由不同脚本负责
- **基础设施** vs **应用配置**: Terraform 负责基础设施，脚本负责应用配置
- **文档** vs **代码**: 分离存放，便于维护

### **2. 模块化设计**
- **独立脚本**: 每个脚本都可以独立运行和测试
- **参数化**: 脚本通过参数接收配置，提高复用性
- **错误隔离**: 一个模块的问题不会影响其他模块

### **3. 可扩展性**
- **可选组件**: 通过注释形式提供可选的开发环境
- **脚本替换**: 可以轻松替换或升级单个脚本
- **配置灵活**: 支持不同的使用场景和需求

### **4. 维护友好**
- **清晰结构**: 文件组织清晰，职责明确
- **文档完整**: 每个组件都有详细说明
- **版本控制**: 便于跟踪变更和回滚

## 🚀 使用流程

### **开发者使用**
1. 下载整个 `TencentCloud/` 目录
2. 根据 `docs/terraform.tfvars.example` 配置参数
3. 打包所有文件上传到 Coder
4. 创建工作空间

### **运维管理**
1. 修改 `scripts/system_setup.sh` 添加新的开发工具
2. 更新 `scripts/juicefs_setup.sh` 优化存储配置
3. 调整 `variables.tf` 添加新的配置参数
4. 更新文档反映变更

### **故障排查**
1. 检查 `user_data.sh` 日志确认 Agent 安装状态
2. 检查 `system_setup.sh` 日志确认系统环境配置
3. 检查 `juicefs_setup.sh` 日志确认存储挂载状态
4. 查看 Terraform 输出获取连接信息

这样的项目结构既保持了代码的清晰性，又提供了良好的可维护性和扩展性。
