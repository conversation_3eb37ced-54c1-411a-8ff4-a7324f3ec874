# Coder 腾讯云 + JuiceFS 云服务 Terraform 模板配置说明
#
# 🏗️ 架构说明：
# 本模板集成了 JuiceFS 云服务，提供持久化的远程开发环境
# 支持竞价实例，数据不会因实例回收而丢失
#
# ==================== 环境变量配置 ====================
#
# 🔐 模板导入时需要设置的敏感参数（管理员配置）：
# 在 Coder 服务器上设置以下环境变量：
#
# export TF_VAR_tencentcloud_cvm_secret_id="your-cvm-secret-id-here"
# export TF_VAR_tencentcloud_cvm_secret_key="your-cvm-secret-key-here"
# export TF_VAR_juicefs_vol_name="dev-workspace"
# export TF_VAR_juicefs_token="your-juicefs-access-token"
# export TF_VAR_tencentcloud_cos_secret_id="your-cos-secret-id"
# export TF_VAR_tencentcloud_cos_secret_key="your-cos-secret-key"
# export TF_VAR_tencentcloud_cos_endpoint="https://your-bucket.cos.ap-hongkong.myqcloud.com"
#
# 📋 获取各项参数的方式：
# - CVM AK/SK：https://console.cloud.tencent.com/cam/capi
# - JuiceFS 令牌：https://juicefs.com/console/
# - COS AK/SK：https://console.cloud.tencent.com/cam/capi
#
# ==================== 用户参数说明 ====================
#
# 🎯 JetBrains 专用开发环境配置说明：
#
# 【用户参数】- 工作空间创建时配置：
# 1. 计费模式：竞价实例（默认）/ 按量付费
# 2. 实例规格：自定义输入（如 S5.MEDIUM4、S5.LARGE8）
# 3. 竞价价格：⚠️ 仅竞价实例时生效（默认 0.023 元/小时）
#
# 【管理员参数】- 模板导入时通过环境变量配置：
# ✅ 以下敏感参数由管理员统一管理：
# - JuiceFS 文件系统名称和访问令牌
# - 腾讯云 COS 存储配置
# - SSH 密钥配置
#
# 【开发工具集成】
# ✅ 推荐使用 JetBrains Gateway 的 Coder 插件：
# - 插件地址：https://plugins.jetbrains.com/plugin/19620-coder
# - 支持所有 JetBrains IDE（IntelliJ IDEA、WebStorm、PyCharm、CLion、RustRover 等）
# - 无需复杂的 SSH 配置，通过 Coder 原生协议连接
#
# 🔧 固定配置（无需用户选择）：
# - 地域：ap-hongkong（香港）
# - 可用区：ap-hongkong-2
# - 操作系统：Debian 13
# - 系统盘：通用型SSD，20GB
# - 网络：预配置VPC、子网、安全组
# - SSH密钥：skey-g266lnub
# - 公网：按流量计费，200M带宽
# - JuiceFS 挂载点：/data/workspaces
# - JuiceFS 缓存：10GB
#
# ==================== 权限要求 ====================
#
# 🔑 所需腾讯云权限：
# - cvm:DescribeInstances
# - cvm:RunInstances
# - cvm:TerminateInstances
# - cvm:DescribeInstancesStatus
# - cvm:ModifyInstancesAttribute
#
# ==================== 使用建议 ====================
#
# 💡 最佳实践：
# 1. 首次使用建议选择 S5.MEDIUM4 实例规格进行测试
# 2. 选择"按量付费"模式便于随时停止
# 3. 使用子账户并分配最小权限原则
# 4. 定期检查账户余额，避免欠费停机
#
# 🚀 快速开始：
# 1. 准备 JuiceFS 云服务和腾讯云 COS 存储桶
# 2. 设置环境变量（CVM AK/SK）
# 3. 上传模板到 Coder
# 4. 创建工作空间，填写所有必要参数
# 5. 等待工作空间初始化完成（包含 JuiceFS 挂载）
# 6. 开始远程开发，数据自动持久化到云存储
# 5. 等待工作空间初始化完成（包含 JuiceFS 挂载）
# 6. 开始远程开发，数据自动持久化到云存储
#
# ==================== JetBrains Gateway 使用指南 ====================
#
# 🚀 推荐的开发工作流程：
#
# 【管理员一次性配置】
# 1. 准备 JuiceFS 云服务和腾讯云 COS 存储桶
# 2. 创建或上传 SSH 密钥到腾讯云（用于备用连接）
# 3. 设置所有环境变量（见上方环境变量配置）
# 4. 上传模板到 Coder 服务器
#
# 【用户日常使用】
# 1. 安装 JetBrains Gateway：https://www.jetbrains.com/remote-development/gateway/
# 2. 安装 Coder 插件：https://plugins.jetbrains.com/plugin/19620-coder
# 3. 配置 Coder 连接：
#    - Server URL: 您的 Coder 服务器地址
#    - Authentication: 使用 Coder 账号登录
# 4. 在 Coder Web UI 中创建工作空间（填写实例配置）
# 5. 在 JetBrains Gateway 中选择工作空间，直接连接
# 6. 开始开发，代码自动同步到 JuiceFS 持久化存储
#
# 🔧 备用方案（故障排查）：
# 如需直接 SSH 连接，请参考工作空间输出中的 ssh_connection 信息
#
# 📞 技术支持：
# 如遇问题，请检查：
# - 环境变量是否正确设置
# - 腾讯云账户权限和余额
# - 实例规格格式是否正确（如 S5.MEDIUM4）
# - JetBrains Gateway Coder 插件是否正确安装和配置
