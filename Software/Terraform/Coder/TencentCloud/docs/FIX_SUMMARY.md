# JetBrains Gateway 连接问题修复总结

## 🎯 问题根因

**模板导入卡在 "unpacking template source archive" 阶段的根本原因**：

官方 `coder/modules` GitHub 仓库已被归档（2025年5月15日），所有模块迁移到新的 Coder Registry。原配置中使用的模块引用地址已失效。

## 🔧 修复内容

### 1. 修复模块引用地址
**修复前**:
```hcl
source = "github.com/coder/modules//jetbrains-gateway"
```

**修复后**:
```hcl
source = "registry.coder.com/coder/jetbrains-gateway/coder"
version = "1.2.2"
```

### 2. 清理冲突配置
- ✅ 移除手动创建的 `coder_app` 资源
- ✅ 移除 startup_script 中的手动 JetBrains 后端注册逻辑
- ✅ 清理所有 coder 用户相关配置，统一使用 root 用户

### 3. 更新监控脚本
- ✅ 修正 JetBrains 后端目录路径为 `/root/.cache/JetBrains/RemoteDev/dist`
- ✅ 更新权限检查逻辑

## 📋 修复后的配置

### JetBrains Gateway 模块配置
```hcl
module "jetbrains_gateway" {
  count          = data.coder_workspace.me.start_count
  source         = "registry.coder.com/coder/jetbrains-gateway/coder"
  version        = "1.2.2"
  agent_id       = coder_agent.main.id
  folder         = "/data/workspaces"
  jetbrains_ides = ["WS", "RR", "PY", "CL"]
  default        = "RR"
  latest         = true
}
```

### 权限配置
```bash
# 确保 root 用户拥有工作空间目录
chown -R root:root /data/workspaces 2>/dev/null || true
```

### 监控脚本
```bash
# 检查是否有已下载的 IDE 后端（使用root用户目录）
BACKENDS_DIR="/root/.cache/JetBrains/RemoteDev/dist"
```

## 🚀 部署步骤

### 1. 验证配置
```bash
# 运行验证脚本
bash scripts/validate_config.sh
```

### 2. 创建部署包
验证脚本会自动生成部署包，包含所有必要文件。

### 3. 上传到 Coder
1. 在 Coder Web UI 中创建新模板
2. 上传生成的 ZIP 文件
3. 配置环境变量

### 4. 环境变量配置
```bash
# 腾讯云 CVM 认证
export TF_VAR_tencentcloud_cvm_secret_id="your-secret-id"
export TF_VAR_tencentcloud_cvm_secret_key="your-secret-key"

# JuiceFS 配置
export TF_VAR_juicefs_vol_name="your-filesystem-name"
export TF_VAR_juicefs_token="your-juicefs-token"

# 腾讯云 COS 配置
export TF_VAR_tencentcloud_cos_secret_id="your-cos-secret-id"
export TF_VAR_tencentcloud_cos_secret_key="your-cos-secret-key"
export TF_VAR_tencentcloud_cos_endpoint="your-cos-endpoint"
```

## ✅ 预期结果

### 模板导入阶段
- ✅ 模板能正常导入，不再卡在 "unpacking" 阶段
- ✅ 显示环境变量配置界面
- ✅ 模板验证通过

### 工作空间创建阶段
- ✅ 工作空间正常创建和启动
- ✅ JuiceFS 正常挂载
- ✅ Coder Agent 正常连接

### JetBrains Gateway 功能
- ✅ 在工作空间 Apps 部分显示 "JetBrains Gateway" 按钮
- ✅ 点击按钮能正常打开 Gateway 选择界面
- ✅ 支持 WebStorm、RustRover、PyCharm、CLion
- ✅ 首次使用时自动下载 IDE 后端
- ✅ 后续连接快速稳定

## 🔍 验证方法

### 1. 基础验证
```bash
# SSH 连接到工作空间
ssh -i ~/.ssh/key.pem root@<workspace-ip>

# 检查 JuiceFS 挂载
mountpoint -q /data/workspaces && echo "已挂载" || echo "未挂载"

# 检查 Coder Agent
ps aux | grep coder

# 运行诊断脚本
bash /path/to/jetbrains_diagnostic.sh
```

### 2. JetBrains Gateway 验证
1. 在 Coder Web UI 中点击 "JetBrains Gateway" 按钮
2. 选择一个 IDE（如 RustRover）
3. 等待 IDE 后端下载完成
4. 验证能正常连接和使用

## 📚 相关文档

- [故障排除指南](JETBRAINS_TROUBLESHOOTING.md)
- [验证清单](VERIFICATION_CHECKLIST.md)
- [项目结构说明](PROJECT_STRUCTURE.md)

## 🎉 总结

通过修复模块引用地址和清理冲突配置，解决了以下问题：
1. ✅ 模板导入卡住问题
2. ✅ JetBrains Gateway 连接问题
3. ✅ 用户权限冲突问题
4. ✅ 配置重复和冲突问题

现在模板应该能正常工作，提供稳定的 JetBrains 远程开发体验。
