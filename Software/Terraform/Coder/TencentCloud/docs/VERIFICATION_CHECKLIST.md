# JetBrains Gateway 修复验证清单

## 📋 部署前检查

### 1. Terraform 配置验证
- [ ] 确认 `module "jetbrains_gateway"` 已启用（第258-267行）
- [ ] 确认手动创建的 `coder_app` 资源已移除
- [ ] 确认 startup_script 中的手动注册代码已清理
- [ ] 确认权限配置使用 `root:root`

### 2. 环境变量配置
- [ ] `TF_VAR_tencentcloud_cvm_secret_id` 已设置
- [ ] `TF_VAR_tencentcloud_cvm_secret_key` 已设置
- [ ] `TF_VAR_juicefs_vol_name` 已设置
- [ ] `TF_VAR_juicefs_token` 已设置
- [ ] `TF_VAR_tencentcloud_cos_secret_id` 已设置
- [ ] `TF_VAR_tencentcloud_cos_secret_key` 已设置
- [ ] `TF_VAR_tencentcloud_cos_endpoint` 已设置

### 3. 网络配置检查
- [ ] 安全组 `sg-9h3craf3` 已开放必要端口
- [ ] VPC 和子网配置正确
- [ ] 公网 IP 分配已启用

## 🚀 部署后验证

### 1. 工作空间创建验证
- [ ] 工作空间创建成功，状态为 "Running"
- [ ] 在 Apps 部分能看到 "JetBrains Gateway" 按钮
- [ ] 元数据显示正常（JuiceFS 挂载状态、Agent 状态等）

### 2. 基础功能验证
- [ ] SSH 连接正常：`ssh -i ~/.ssh/key.pem root@<IP>`
- [ ] JuiceFS 挂载正常：`mountpoint -q /data/workspaces`
- [ ] Coder Agent 运行正常：`ps aux | grep coder`
- [ ] 网络连接正常：`curl -s https://coder.lilh.net/healthz`

### 3. JetBrains Gateway 验证
- [ ] 点击 "JetBrains Gateway" 按钮能正常打开 Gateway
- [ ] 能选择不同的 IDE（WebStorm、RustRover、PyCharm、CLion）
- [ ] 首次连接能正常下载 IDE 后端
- [ ] IDE 后端下载完成后能正常连接

### 4. 诊断工具验证
- [ ] 运行诊断脚本：`bash scripts/jetbrains_diagnostic.sh`
- [ ] 所有检查项显示正常或预期状态
- [ ] 监控元数据更新正常

## 🐛 故障排查步骤

### 如果 Gateway 按钮不显示
1. 检查 Terraform 状态：`terraform plan`
2. 重新应用配置：`terraform apply`
3. 查看模块输出：`terraform output`

### 如果点击按钮无响应
1. 确认本地安装了 JetBrains Gateway
2. 检查浏览器是否允许 `jetbrains-gateway://` 协议
3. 尝试手动复制 URL 到 Gateway 客户端

### 如果 IDE 下载失败
1. 运行诊断脚本检查网络连接
2. 检查磁盘空间：`df -h`
3. 检查下载目录权限：`ls -la /root/.cache/`
4. 手动测试下载：`curl -I https://download.jetbrains.com/`

### 如果连接超时
1. 检查 Agent 状态：`ps aux | grep coder`
2. 检查端口监听：`netstat -tlnp | grep coder`
3. 检查防火墙：`ufw status`
4. 重启工作空间

## 📊 性能基准

### 预期性能指标
- **工作空间启动时间**: 3-5 分钟（包含 JuiceFS 挂载）
- **首次 IDE 下载时间**: 3-5 分钟（取决于网络速度）
- **后续连接时间**: 30-60 秒
- **内存使用**: 基础环境 ~1GB，IDE 运行时 +1-2GB

### 资源要求
- **最小配置**: S5.MEDIUM4 (1核4GB)
- **推荐配置**: S5.LARGE8 (2核8GB)
- **高性能配置**: S5.XLARGE16 (4核16GB)

## ✅ 验证完成标准

### 基础功能验证通过
- [ ] 工作空间正常启动和运行
- [ ] JuiceFS 挂载成功
- [ ] Coder Agent 连接正常
- [ ] 网络连接稳定

### JetBrains Gateway 功能验证通过
- [ ] Gateway 按钮正常显示和响应
- [ ] 至少一个 IDE 能成功下载和连接
- [ ] IDE 界面正常加载，能访问 `/data/workspaces` 目录
- [ ] 能正常编辑文件和运行代码

### 监控和诊断功能验证通过
- [ ] 元数据监控正常更新
- [ ] 诊断脚本运行无错误
- [ ] 日志记录完整且可访问

## 📝 验证报告模板

```
验证日期: ___________
验证人员: ___________

基础功能:
- 工作空间创建: ✅/❌
- JuiceFS 挂载: ✅/❌
- Agent 连接: ✅/❌
- 网络连接: ✅/❌

JetBrains Gateway:
- 按钮显示: ✅/❌
- IDE 下载: ✅/❌ (测试的 IDE: _______)
- 连接成功: ✅/❌
- 功能正常: ✅/❌

问题记录:
- 问题1: ___________
- 解决方案: ___________

总体评估: ✅ 通过 / ❌ 需要修复

备注: ___________
```

## 🔗 相关文档

- [故障排除指南](JETBRAINS_TROUBLESHOOTING.md)
- [项目结构说明](PROJECT_STRUCTURE.md)
- [配置示例](terraform.tfvars.example)
