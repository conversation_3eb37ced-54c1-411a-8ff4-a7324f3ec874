# 紧急修复：模板导入卡住问题

## 🚨 问题现象
- 模板上传后卡在 "unpacking template source archive" 阶段
- Web界面无响应，无法显示环境变量配置步骤
- 服务端日志显示解包完成但无后续进展

## 🎯 根本原因
**Terraform 配置参数冲突**：JetBrains Gateway 模块中同时使用了：
- `version = "1.2.2"` (指定具体版本)
- `latest = true` (使用最新版本)

这两个参数互斥，导致 Terraform 验证失败，模板导入过程中断。

## 🔧 紧急修复

### 修复内容
```hcl
# 修复前（有问题的配置）
module "jetbrains_gateway" {
  count          = data.coder_workspace.me.start_count
  source         = "registry.coder.com/coder/jetbrains-gateway/coder"
  version        = "1.2.2"
  agent_id       = coder_agent.main.id
  folder         = "/data/workspaces"
  jetbrains_ides = ["WS", "RR", "PY", "CL"]  # 可能有问题
  default        = "RR"                       # 可能有问题
  latest         = true                       # 与 version 冲突！
}

# 修复后（简化配置）
module "jetbrains_gateway" {
  count    = data.coder_workspace.me.start_count
  source   = "registry.coder.com/coder/jetbrains-gateway/coder"
  version  = "1.2.2"
  agent_id = coder_agent.main.id
  folder   = "/data/workspaces"
}
```

### 关键变更
1. ✅ **移除冲突参数**: 删除 `latest = true`
2. ✅ **简化配置**: 移除可能有问题的 `jetbrains_ides` 和 `default` 参数
3. ✅ **保持核心功能**: 保留必要的 `agent_id` 和 `folder` 参数

## 🚀 立即测试步骤

### 1. 验证修复
```bash
# 运行快速验证脚本
cd Software/Terraform/Coder/TencentCloud
bash scripts/quick_fix_verify.sh
```

### 2. 重新部署
1. 使用验证脚本生成的新 ZIP 包
2. 在 Coder 中创建新模板
3. 上传修复后的配置
4. 检查是否显示环境变量配置界面

### 3. 预期结果
- ✅ 模板能正常导入，不再卡住
- ✅ 显示环境变量配置界面
- ✅ 能成功创建工作空间
- ✅ JetBrains Gateway 按钮正常显示

## 🔄 备用方案

如果官方模块仍有问题，使用手动配置：

### 启用备用方案
```bash
# 1. 启用手动配置
mv jetbrains_manual_config.tf.backup jetbrains_manual_config.tf

# 2. 注释掉官方模块（在 tencentcloud.tf 中）
# module "jetbrains_gateway" {
#   count    = data.coder_workspace.me.start_count
#   source   = "registry.coder.com/coder/jetbrains-gateway/coder"
#   version  = "1.2.2"
#   agent_id = coder_agent.main.id
#   folder   = "/data/workspaces"
# }

# 3. 重新打包上传
```

### 备用方案特点
- ✅ 手动创建 JetBrains Gateway 按钮
- ✅ 支持多个 IDE（WebStorm、RustRover、PyCharm、CLion）
- ✅ 已修复权限问题（使用 root 用户）
- ✅ 使用标准 jetbrains-gateway:// 协议

## 📊 修复验证清单

### 配置检查
- [ ] 确认移除了 `latest = true` 参数
- [ ] 确认使用正确的 registry 地址
- [ ] 确认版本锁定为 `1.2.2`
- [ ] 确认没有手动创建的冲突应用

### 功能测试
- [ ] 模板能正常导入
- [ ] 显示环境变量配置界面
- [ ] 工作空间能正常创建
- [ ] JetBrains Gateway 按钮显示
- [ ] 能正常连接 IDE

## 🎉 总结

通过移除参数冲突和简化模块配置，解决了模板导入卡住的问题。这是一个典型的 Terraform 配置错误，突出了参数验证的重要性。

**关键教训**：
1. 模块参数需要仔细验证兼容性
2. 版本锁定和 `latest` 参数不能同时使用
3. 简化配置通常比复杂配置更稳定
4. 始终准备备用方案

现在模板应该能正常工作了！
