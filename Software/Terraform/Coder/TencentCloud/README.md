# Coder 腾讯云 CVM + JuiceFS 模板

这是一个为 [Coder](https://github.com/coder/coder) 开源项目设计的企业级 Terraform 模板，集成了 JuiceFS 云服务，提供持久化的远程开发环境。

## 🎯 设计目标

- ✅ **数据持久化**: 集成 JuiceFS 云服务，确保代码和数据安全
- ✅ **竞价实例友好**: 支持竞价实例，数据不会因实例回收而丢失
- ✅ **JetBrains 专用**: 专为 JetBrains IDE 优化的远程开发环境
- ✅ **简洁配置**: 避免复杂的多工具集成，专注于稳定的开发体验
- ✅ **自动化部署**: 一键创建完整的开发环境

## 📋 文件结构

```
Software/Terraform/Coder/
├── tencentcloud.tf          # 主配置文件
├── variables.tf             # 变量定义
├── outputs.tf              # 输出配置
├── terraform.tfvars.example # 配置示例
└── README.md               # 本文件
```

## ⚡ 快速开始

### 1. 准备 JuiceFS 云服务
在使用模板前，需要先准备 JuiceFS 环境：
1. 注册 [JuiceFS 云服务](https://juicefs.com/zh-cn/cloud)
2. 创建文件系统并获取访问令牌
3. 在腾讯云创建 COS 存储桶（建议香港地域）
4. 获取 COS 的 Access Key 和 Secret Key

### 2. 配置全局环境变量
在 Coder 服务器上设置腾讯云 CVM 认证信息：
```bash
# 在 Coder 服务器环境中设置
export TF_VAR_tencentcloud_cvm_secret_id="your-cvm-secret-id"
export TF_VAR_tencentcloud_cvm_secret_key="your-cvm-secret-key"
```

### 3. 上传模板到 Coder
```bash
# 将整个 TencentCloud 目录打包成 ZIP，包含：
#
# 📁 TencentCloud/
# ├── README.md              # 主要说明文档
# ├── tencentcloud.tf        # Terraform 主配置
# ├── variables.tf           # 参数定义
# ├── outputs.tf             # 输出配置
# ├── scripts/               # 脚本文件目录
# │   ├── user_data.sh      # Coder Agent 安装
# │   ├── system_setup.sh   # 系统环境配置
# │   └── juicefs_setup.sh  # JuiceFS 配置
# └── docs/                  # 文档目录
#     ├── PROJECT_STRUCTURE.md
#     └── terraform.tfvars.example

# 在 Coder Web UI 中上传 ZIP 文件创建模板
```

### 4. 创建工作空间
在 Coder Web UI 中：
1. 选择上传的模板
2. 填写必要的参数：
   - **CVM 配置**: 计费模式、实例规格、竞价价格
   - **JuiceFS 配置**: 文件系统名称、访问令牌
   - **COS 配置**: Access Key、Secret Key、Endpoint 地址
3. 点击创建工作空间

### 5. 连接到工作空间

#### 🚀 推荐方式：JetBrains Gateway Coder 插件
1. **安装插件**：
   - 打开 JetBrains Gateway
   - 安装 "Coder" 插件：https://plugins.jetbrains.com/plugin/19620-coder

2. **配置连接**：
   - 输入 Coder Server 地址
   - 使用您的 Coder 账号登录
   - 选择工作空间，直接连接

3. **开始开发**：
   - 插件自动处理连接和项目同步
   - 无需配置 SSH 密钥或复杂的网络设置
   - 支持所有 JetBrains IDE（IntelliJ IDEA、WebStorm、PyCharm、CLion、RustRover 等）

#### 🔧 备用方式：直接 SSH 连接
仅用于故障排查或高级用户需求：
- **SSH 连接**: `ssh -i ~/.ssh/coder-key.pem root@<公网IP>`
- **端口转发**: 通过 SSH helper 获取详细配置信息

### 6. 工作空间目录结构
```
/data/workspaces/              # JuiceFS 挂载点（共享工作空间）
├── project-1/                 # 项目代码仓库 1
├── project-2/                 # 项目代码仓库 2
├── shared-configs/            # 共享配置文件
└── build-cache/               # 构建缓存

/root/workspace -> /data/workspaces  # 快捷访问链接
```

**注意**：
- 所有用户共享同一个工作空间，适合团队协作开发
- 代码仓库直接克隆到 `/data/workspaces` 目录下
- 系统使用 root 用户运行，简化权限管理
- 采用模块化脚本设计，便于后续扩展和维护

## 🏗️ 模块化架构设计

### **脚本职责分离：**
- **`user_data.sh`**: Coder Agent 安装（系统启动时执行）
- **`system_setup.sh`**: 系统环境和开发工具配置
- **`juicefs_setup.sh`**: JuiceFS 云服务专门配置
- **`tencentcloud.tf`**: 协调各个模块的执行

### **扩展开发环境：**
如需添加特定开发环境（如 Rust、Node.js、Python），只需修改 `scripts/system_setup.sh`：
```bash
# 启用 scripts/system_setup.sh 中的相应注释部分
# 例如启用 Rust 环境：
echo "🦀 安装 Rust 开发环境..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
```

## 📚 详细文档

- **项目结构说明**: 查看 `docs/PROJECT_STRUCTURE.md`
- **配置参数说明**: 查看 `docs/terraform.tfvars.example`
- **脚本功能说明**: 查看 `scripts/` 目录中各脚本的注释

## 🏗️ 架构设计

### JetBrains 专用开发环境

#### 🎯 设计理念
- **专注性**：专为 JetBrains IDE 优化，避免多工具集成的复杂性
- **稳定性**：使用 Coder 原生集成，减少外部依赖和配置错误
- **简洁性**：最小化用户配置，管理员统一管理敏感参数

#### 🔧 用户参数（工作空间创建时配置）

**CVM 实例配置：**
| 参数 | 描述 | 默认值 | 选项 |
|------|------|--------|------|
| **计费模式** | 付费方式 | 竞价实例 | 竞价实例/按量付费 |
| **实例规格** | CPU/内存配置 | S5.MEDIUM4 | 自定义输入（如 S5.LARGE8） |
| **竞价价格** | 最高出价 | 0.023元/小时 | 自定义输入（⚠️仅竞价实例时生效） |

#### 🔒 管理员配置（模板导入时设置）

**敏感参数通过环境变量配置：**
| 参数 | 环境变量 | 说明 |
|------|----------|------|
| **JuiceFS 文件系统名称** | `TF_VAR_juicefs_vol_name` | JuiceFS 云服务中的文件系统名称 |
| **JuiceFS 访问令牌** | `TF_VAR_juicefs_token` | JuiceFS 云服务访问令牌（敏感） |
| **COS SecretId** | `TF_VAR_tencentcloud_cos_secret_id` | 腾讯云 COS 访问密钥（敏感） |
| **COS SecretKey** | `TF_VAR_tencentcloud_cos_secret_key` | 腾讯云 COS 访问密钥（敏感） |
| **COS Endpoint** | `TF_VAR_tencentcloud_cos_endpoint` | COS 存储桶完整地址 |
| **SSH 密钥 ID** | `TF_VAR_ssh_key_id` | 腾讯云 SSH 密钥 ID |

#### 🔒 模板级别的固定配置
| 参数 | 值 | 说明 |
|------|-----|------|
| **地域** | ap-hongkong | 香港地域 |
| **可用区** | ap-hongkong-2 | 香港可用区2 |
| **操作系统** | img-5s7vueks | Debian 13 |
| **系统盘类型** | CLOUD_PREMIUM | 通用型SSD云硬盘 |
| **系统盘大小** | 20GB | 标准容量 |
| **VPC网络** | vpc-kpqmh20m | 预配置VPC |
| **子网** | subnet-d7k6whm5 | 预配置子网 |
| **安全组** | sg-9h3craf3 | 预配置安全组 |
| **SSH密钥** | skey-g266lnub | 团队共享密钥 |
| **公网配置** | 按流量计费, 200M带宽 | 标准化网络配置 |

#### 🌐 全局环境变量
| 变量 | 说明 |
|------|------|
| `TF_VAR_tencentcloud_cvm_secret_id` | 腾讯云 CVM API 密钥 ID |
| `TF_VAR_tencentcloud_cvm_secret_key` | 腾讯云 CVM API 密钥 |

## 💰 推荐实例规格

| 规格 | 配置 | 适用场景 |
|------|------|----------|
| `S5.MEDIUM2` | 1核2GB | 轻量开发、测试 |
| `S5.LARGE4` | 2核4GB | 标准开发 |
| `S5.XLARGE8` | 4核8GB | 高性能开发 |

## 📊 输出信息

模板会输出以下信息：
- 实例 ID、名称、规格
- 公网和私网 IP 地址
- SSH 连接命令

## 🐛 故障排除

### 常见问题
1. **实例创建失败**: 检查账户余额和 API 权限
2. **网络连接问题**: 验证安全组配置
3. **认证问题**: 确认 SSH 密钥配置正确

### 调试命令
```bash
# 查看详细日志
terraform apply -auto-approve

# 检查实例状态
terraform show

# 销毁实例（测试完成后）
terraform destroy
```

## 📝 注意事项

1. 这是简化版本，专注于基础功能验证
2. 成功创建实例后，可以逐步添加更多功能
3. 使用前请确保了解腾讯云计费规则
4. 测试完成后记得销毁资源以避免费用

---

**提示**: 这个简化版本旨在确保基础功能正常工作，后续可以根据需要逐步扩展功能。
