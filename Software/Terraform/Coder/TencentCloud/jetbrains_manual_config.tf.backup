# 备用方案：手动配置 JetBrains Gateway（如果官方模块有问题时使用）
# 使用方法：将此文件重命名为 jetbrains_manual_config.tf 并注释掉 tencentcloud.tf 中的官方模块

# 手动创建 JetBrains Gateway 应用（修复权限问题后的版本）
resource "coder_app" "jetbrains_gateway" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "jetbrains-gateway"
  display_name = "JetBrains Gateway"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/Gateway_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}"
  external     = true
}

# WebStorm 专用按钮
resource "coder_app" "webstorm" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "webstorm"
  display_name = "WebStorm"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/WebStorm_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}&ide_product_code=WS"
  external     = true
}

# RustRover 专用按钮
resource "coder_app" "rustrover" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "rustrover"
  display_name = "RustRover"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/RustRover_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}&ide_product_code=RR"
  external     = true
}

# PyCharm 专用按钮
resource "coder_app" "pycharm" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "pycharm"
  display_name = "PyCharm Professional"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/PyCharm_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}&ide_product_code=PY"
  external     = true
}

# CLion 专用按钮
resource "coder_app" "clion" {
  count        = data.coder_workspace.me.start_count
  agent_id     = coder_agent.main.id
  slug         = "clion"
  display_name = "CLion"
  icon         = "https://resources.jetbrains.com/storage/products/company/brand/logos/CLion_icon.png"
  url          = "jetbrains-gateway://connect#type=coder&workspace=${data.coder_workspace.me.name}&agent=main&folder=/data/workspaces&url=${data.coder_workspace.me.access_url}&token=${coder_agent.main.token}&ide_product_code=CL"
  external     = true
}

# 注意事项：
# 1. 此配置已修复权限问题（使用root用户）
# 2. 移除了手动后端注册逻辑，依赖按需下载
# 3. 提供了多个IDE的独立按钮
# 4. 使用标准的 jetbrains-gateway:// 协议URL
