#!/bin/bash
# 快速验证修复后的配置

set -e

echo "🔍 JetBrains Gateway 配置修复验证"
echo "================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo "1. 检查关键修复"
echo "---------------"

# 检查是否移除了冲突参数
if grep -q "latest.*=.*true" tencentcloud.tf; then
    check_status 1 "发现冲突参数 'latest = true'，需要移除"
else
    check_status 0 "已移除冲突参数 'latest = true'"
fi

# 检查模块配置
if grep -q "registry.coder.com/coder/jetbrains-gateway/coder" tencentcloud.tf; then
    check_status 0 "JetBrains Gateway 模块使用正确的 registry 地址"
else
    check_status 1 "JetBrains Gateway 模块地址不正确"
fi

# 检查版本配置
if grep -q 'version.*=.*"1.2.2"' tencentcloud.tf; then
    check_status 0 "模块版本已锁定为 1.2.2"
else
    warning "建议锁定模块版本"
fi

# 检查是否有手动创建的冲突应用
if grep -q "coder_app.*jetbrains" tencentcloud.tf; then
    check_status 1 "发现手动创建的 JetBrains 应用，可能与官方模块冲突"
else
    check_status 0 "已移除手动创建的 JetBrains 应用"
fi

echo ""
echo "2. 检查配置完整性"
echo "-----------------"

# 检查必要的 data 源
REQUIRED_DATA_SOURCES=(
    "data.coder_workspace.me"
    "data.coder_workspace_owner.me"
)

for data_source in "${REQUIRED_DATA_SOURCES[@]}"; do
    if grep -q "$data_source" tencentcloud.tf; then
        check_status 0 "数据源存在: $data_source"
    else
        check_status 1 "数据源缺失: $data_source"
    fi
done

# 检查 Coder Agent 配置
if grep -q "resource.*coder_agent.*main" tencentcloud.tf; then
    check_status 0 "Coder Agent 配置存在"
else
    check_status 1 "Coder Agent 配置缺失"
fi

echo ""
echo "3. Terraform 语法检查"
echo "---------------------"

if command -v terraform >/dev/null 2>&1; then
    info "运行 Terraform 验证..."
    
    # 创建临时目录进行验证
    TEMP_DIR=$(mktemp -d)
    cp *.tf "$TEMP_DIR/" 2>/dev/null || true
    cp -r scripts "$TEMP_DIR/" 2>/dev/null || true
    
    cd "$TEMP_DIR"
    
    if terraform init -backend=false >/dev/null 2>&1; then
        if terraform validate >/dev/null 2>&1; then
            check_status 0 "Terraform 配置语法正确"
        else
            check_status 1 "Terraform 配置语法错误"
            echo ""
            echo "详细错误信息:"
            terraform validate
        fi
    else
        warning "Terraform 初始化失败，跳过语法检查"
    fi
    
    cd - >/dev/null
    rm -rf "$TEMP_DIR"
else
    warning "Terraform 未安装，跳过语法检查"
fi

echo ""
echo "4. 生成测试包"
echo "-------------"

# 生成测试包
PACKAGE_NAME="coder-tencentcloud-fixed-$(date +%Y%m%d-%H%M%S).zip"
info "创建测试包: $PACKAGE_NAME"

zip -r "$PACKAGE_NAME" . \
    -x "*.git*" \
    -x "*.terraform*" \
    -x "terraform.tfstate*" \
    -x "*.zip" \
    -x "quick_fix_verify.sh" \
    -x "*.backup" \
    >/dev/null 2>&1

if [ $? -eq 0 ]; then
    check_status 0 "测试包创建成功: $PACKAGE_NAME"
    PACKAGE_SIZE=$(du -h "$PACKAGE_NAME" | cut -f1)
    echo "   包大小: $PACKAGE_SIZE"
else
    check_status 1 "测试包创建失败"
fi

echo ""
echo "✅ 修复验证完成！"
echo ""
echo "📋 修复总结:"
echo "- 移除了 'latest = true' 冲突参数"
echo "- 简化了 JetBrains Gateway 模块配置"
echo "- 保持了正确的 registry 地址和版本锁定"
echo ""
echo "🚀 测试步骤:"
echo "1. 上传 $PACKAGE_NAME 到 Coder"
echo "2. 创建新模板"
echo "3. 检查是否显示环境变量配置界面"
echo ""
echo "🔄 如果仍有问题:"
echo "1. 使用备用方案: mv jetbrains_manual_config.tf.backup jetbrains_manual_config.tf"
echo "2. 注释掉 tencentcloud.tf 中的官方模块配置"
echo "3. 重新打包上传"
