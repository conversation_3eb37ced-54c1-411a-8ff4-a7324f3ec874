#!/bin/bash
set -e

# Coder Agent 安装和启动脚本
# 此脚本在 CVM 实例首次启动时执行 (以 root 用户运行)
# 遵循 Coder 官方最佳实践

echo "🚀 开始初始化 Coder 工作空间..."

# 安装 Coder Agent 运行的基础依赖
echo "📦 安装基础系统依赖..."
apt-get update -qq
apt-get install -y -qq \
    curl \
    wget \
    systemd \
    dnsutils \
    ca-certificates

# 验证 Coder 服务可用性
echo "🌐 验证 Coder 服务连通性..."
CODER_DOMAIN="coder.lilh.net"
if curl --connect-timeout 10 --silent --head "https://$CODER_DOMAIN" >/dev/null 2>&1; then
    echo "✅ Coder 服务连接正常: $CODER_DOMAIN"
else
    echo "⚠️  Coder 服务连接异常: $CODER_DOMAIN"
    echo "🔍 DNS 解析测试..."
    if nslookup "$CODER_DOMAIN" >/dev/null 2>&1; then
        echo "✅ DNS 解析成功: $CODER_DOMAIN"
    else
        echo "❌ DNS 解析失败: $CODER_DOMAIN"
    fi
    echo "⚠️  继续尝试 Agent 安装..."
fi

# 创建日志目录
mkdir -p /tmp

# 准备 Coder Agent 环境
echo "🔧 准备 Coder Agent 环境..."
cd /root

# 解码 Coder Agent 初始化脚本
echo "📝 解码 Agent 初始化脚本..."
echo "${init_script}" | base64 -d > /tmp/coder_init.sh
chmod +x /tmp/coder_init.sh

# 设置环境变量并启动 Agent
echo "🚀 启动 Coder Agent..."
export CODER_AGENT_TOKEN="${coder_agent_token}"

# 执行 Agent 初始化脚本，并记录日志
echo "📋 执行 Agent 初始化脚本..."
/tmp/coder_init.sh 2>&1 | tee /tmp/coder-user-data.log

# 检查 Agent 启动状态
if pgrep -f "coder agent" >/dev/null; then
    echo "✅ Coder Agent 进程已启动"
else
    echo "⚠️  未检测到 Coder Agent 进程"
fi

# 清理临时文件
rm -f /tmp/coder_init.sh

echo "✅ Coder Agent 初始化完成"
echo "💡 Agent startup script 将在连接建立后自动执行"
echo "📋 相关日志文件："
echo "   - Agent 日志: /tmp/coder-agent.log"
echo "   - Startup Script 日志: /tmp/coder-startup-script.log"
echo "   - User Data 日志: /tmp/coder-user-data.log"
