#!/bin/bash
# Terraform 配置验证脚本
# 用于在上传到 Coder 之前验证配置语法

set -e

echo "🔍 Terraform 配置验证工具"
echo "=========================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查当前目录
CURRENT_DIR=$(pwd)
echo "当前目录: $CURRENT_DIR"

# 检查是否在正确的目录
if [[ ! "$CURRENT_DIR" == *"TencentCloud"* ]]; then
    warning "请在 TencentCloud 目录中运行此脚本"
fi

echo ""
echo "1. 检查必要文件"
echo "---------------"

# 检查主要文件是否存在
REQUIRED_FILES=(
    "tencentcloud.tf"
    "variables.tf"
    "outputs.tf"
    "scripts/user_data.sh"
    "scripts/system_setup.sh"
    "scripts/juicefs_setup.sh"
)

ALL_FILES_EXIST=true
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        check_status 0 "文件存在: $file"
    else
        check_status 1 "文件缺失: $file"
        ALL_FILES_EXIST=false
    fi
done

if [ "$ALL_FILES_EXIST" = false ]; then
    echo -e "${RED}❌ 存在缺失文件，请检查项目结构${NC}"
    exit 1
fi

echo ""
echo "2. 检查 Terraform 语法"
echo "----------------------"

# 检查 Terraform 是否安装
if command -v terraform >/dev/null 2>&1; then
    check_status 0 "Terraform 已安装: $(terraform version | head -1)"
else
    warning "Terraform 未安装，跳过语法检查"
    echo "请安装 Terraform: https://www.terraform.io/downloads"
    exit 0
fi

# 初始化 Terraform（仅下载 provider schema）
echo ""
info "初始化 Terraform..."
if terraform init -backend=false >/dev/null 2>&1; then
    check_status 0 "Terraform 初始化成功"
else
    check_status 1 "Terraform 初始化失败"
    echo "请检查 provider 配置"
    exit 1
fi

# 验证配置语法
echo ""
info "验证 Terraform 配置语法..."
if terraform validate >/dev/null 2>&1; then
    check_status 0 "Terraform 配置语法正确"
else
    check_status 1 "Terraform 配置语法错误"
    echo ""
    echo "详细错误信息:"
    terraform validate
    exit 1
fi

# 格式化检查
echo ""
info "检查代码格式..."
if terraform fmt -check >/dev/null 2>&1; then
    check_status 0 "代码格式正确"
else
    warning "代码格式需要调整"
    echo "运行 'terraform fmt' 来自动格式化代码"
fi

echo ""
echo "3. 检查关键配置"
echo "---------------"

# 检查 JetBrains Gateway 模块配置
if grep -q "registry.coder.com/coder/jetbrains-gateway/coder" tencentcloud.tf; then
    check_status 0 "JetBrains Gateway 模块使用正确的 registry 地址"
else
    check_status 1 "JetBrains Gateway 模块地址可能不正确"
fi

# 检查版本号
if grep -q 'version.*=.*"1.2.2"' tencentcloud.tf; then
    check_status 0 "JetBrains Gateway 模块版本已锁定"
else
    warning "建议锁定 JetBrains Gateway 模块版本"
fi

# 检查是否移除了手动配置
if grep -q "coder_app.*jetbrains" tencentcloud.tf; then
    check_status 1 "发现手动创建的 JetBrains 应用，可能与官方模块冲突"
else
    check_status 0 "已移除手动创建的 JetBrains 应用"
fi

# 检查用户权限配置
if grep -q "coder:coder" tencentcloud.tf; then
    check_status 1 "发现 coder 用户配置，可能导致权限问题"
else
    check_status 0 "用户权限配置正确（使用 root）"
fi

echo ""
echo "4. 检查脚本文件"
echo "---------------"

# 检查脚本文件语法
SCRIPT_FILES=(
    "scripts/user_data.sh"
    "scripts/system_setup.sh"
    "scripts/juicefs_setup.sh"
)

for script in "${SCRIPT_FILES[@]}"; do
    if bash -n "$script" 2>/dev/null; then
        check_status 0 "脚本语法正确: $script"
    else
        check_status 1 "脚本语法错误: $script"
        echo "详细错误:"
        bash -n "$script"
    fi
done

echo ""
echo "5. 生成部署包"
echo "-------------"

# 创建部署包
PACKAGE_NAME="coder-tencentcloud-$(date +%Y%m%d-%H%M%S).zip"
info "创建部署包: $PACKAGE_NAME"

# 排除不必要的文件
zip -r "$PACKAGE_NAME" . \
    -x "*.git*" \
    -x "*.terraform*" \
    -x "terraform.tfstate*" \
    -x "*.zip" \
    -x "validate_config.sh" \
    >/dev/null 2>&1

if [ $? -eq 0 ]; then
    check_status 0 "部署包创建成功: $PACKAGE_NAME"
    PACKAGE_SIZE=$(du -h "$PACKAGE_NAME" | cut -f1)
    echo "   包大小: $PACKAGE_SIZE"
else
    check_status 1 "部署包创建失败"
fi

echo ""
echo "✅ 验证完成！"
echo ""
echo "📋 总结:"
echo "- 配置文件语法正确"
echo "- JetBrains Gateway 模块配置正确"
echo "- 脚本文件语法正确"
echo "- 部署包已生成: $PACKAGE_NAME"
echo ""
echo "🚀 下一步:"
echo "1. 将 $PACKAGE_NAME 上传到 Coder"
echo "2. 创建新的模板"
echo "3. 配置环境变量"
echo "4. 创建工作空间进行测试"
echo ""
echo "📚 相关文档:"
echo "- 故障排除: docs/JETBRAINS_TROUBLESHOOTING.md"
echo "- 验证清单: docs/VERIFICATION_CHECKLIST.md"
