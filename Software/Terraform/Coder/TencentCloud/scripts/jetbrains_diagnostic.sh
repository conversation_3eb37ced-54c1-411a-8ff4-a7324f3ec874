#!/bin/bash
# JetBrains Gateway 连接诊断脚本
# 用于排查 JetBrains 远程开发连接问题

set -e

echo "🔍 JetBrains Gateway 连接诊断工具"
echo "=================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo "1. 系统基础检查"
echo "----------------"

# 检查用户
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"
if [ "$CURRENT_USER" = "root" ]; then
    check_status 0 "使用 root 用户运行"
else
    check_status 1 "当前用户不是 root，可能存在权限问题"
fi

# 检查系统资源
echo ""
echo "系统资源状态:"
echo "CPU 核心数: $(nproc)"
MEM_TOTAL_MB=$(free -m | grep '^Mem:' | awk '{print $2}')
MEM_TOTAL_GB=$((MEM_TOTAL_MB / 1024))
echo "内存总量: ${MEM_TOTAL_GB}GB"
DISK_AVAIL=$(df -h / | tail -1 | awk '{print $4}')
echo "可用磁盘空间: $DISK_AVAIL"

echo ""
echo "2. Coder Agent 检查"
echo "-------------------"

# 检查 Coder Agent 进程
if pgrep -f "coder.*agent" >/dev/null 2>&1; then
    check_status 0 "Coder Agent 进程运行中"
    AGENT_PID=$(pgrep -f "coder.*agent")
    echo "   进程 ID: $AGENT_PID"
else
    check_status 1 "Coder Agent 进程未运行"
fi

# 检查 Agent Token
if [ -n "$CODER_AGENT_TOKEN" ]; then
    check_status 0 "Coder Agent Token 已设置"
    echo "   Token 前缀: $(echo $CODER_AGENT_TOKEN | cut -c1-12)..."
else
    check_status 1 "Coder Agent Token 未设置"
fi

# 检查 Agent 端口
if [ -n "$CODER_AGENT_TOKEN" ]; then
    AGENT_PORT=$(echo $CODER_AGENT_TOKEN | tail -c 5)
    if netstat -tlnp 2>/dev/null | grep ":$AGENT_PORT" >/dev/null; then
        check_status 0 "Agent 端口 $AGENT_PORT 正在监听"
    else
        check_status 1 "Agent 端口 $AGENT_PORT 未监听"
    fi
fi

echo ""
echo "3. JuiceFS 挂载检查"
echo "-------------------"

# 检查 JuiceFS 挂载
if mountpoint -q /data/workspaces 2>/dev/null; then
    check_status 0 "JuiceFS 已挂载到 /data/workspaces"
    MOUNT_INFO=$(df -h /data/workspaces | tail -1)
    echo "   挂载信息: $MOUNT_INFO"
else
    check_status 1 "JuiceFS 未挂载到 /data/workspaces"
fi

# 检查工作空间目录权限
if [ -d "/data/workspaces" ]; then
    WORKSPACE_OWNER=$(stat -c '%U:%G' /data/workspaces)
    if [ "$WORKSPACE_OWNER" = "root:root" ]; then
        check_status 0 "工作空间目录权限正确 (root:root)"
    else
        check_status 1 "工作空间目录权限异常: $WORKSPACE_OWNER"
    fi
else
    check_status 1 "工作空间目录 /data/workspaces 不存在"
fi

echo ""
echo "4. JetBrains 环境检查"
echo "---------------------"

# 检查 JetBrains 缓存目录
JETBRAINS_CACHE_DIR="/root/.cache/JetBrains"
if [ -d "$JETBRAINS_CACHE_DIR" ]; then
    check_status 0 "JetBrains 缓存目录存在"
    echo "   目录: $JETBRAINS_CACHE_DIR"
else
    warning "JetBrains 缓存目录不存在（首次使用时会自动创建）"
fi

# 检查已下载的 IDE
JETBRAINS_DIST_DIR="/root/.cache/JetBrains/RemoteDev/dist"
if [ -d "$JETBRAINS_DIST_DIR" ] && [ "$(ls -A $JETBRAINS_DIST_DIR 2>/dev/null)" ]; then
    INSTALLED_COUNT=$(ls -1 "$JETBRAINS_DIST_DIR" 2>/dev/null | wc -l)
    check_status 0 "已下载 $INSTALLED_COUNT 个 IDE 后端"
    echo "   已安装的 IDE:"
    ls -1 "$JETBRAINS_DIST_DIR" 2>/dev/null | sed 's/^/     - /'
else
    warning "未发现已下载的 IDE 后端（首次使用时会自动下载）"
fi

# 检查 JetBrains 进程
if pgrep -f "remote-dev-server" >/dev/null 2>&1; then
    check_status 0 "JetBrains 远程开发服务器运行中"
    JETBRAINS_PIDS=$(pgrep -f "remote-dev-server")
    echo "   进程 ID: $JETBRAINS_PIDS"
else
    info "JetBrains 远程开发服务器未运行（连接时会自动启动）"
fi

echo ""
echo "5. 网络连接检查"
echo "---------------"

# 检查 Coder 服务器连接
CODER_SERVER="coder.lilh.net"
if curl -s --connect-timeout 5 "https://$CODER_SERVER/healthz" >/dev/null 2>&1; then
    check_status 0 "Coder 服务器连接正常"
else
    check_status 1 "Coder 服务器连接失败"
fi

# 检查 DNS 解析
if nslookup "$CODER_SERVER" >/dev/null 2>&1; then
    check_status 0 "DNS 解析正常"
else
    check_status 1 "DNS 解析失败"
fi

# 检查 JetBrains 下载服务器连接
if curl -I --connect-timeout 5 "https://download.jetbrains.com/" >/dev/null 2>&1; then
    check_status 0 "JetBrains 下载服务器连接正常"
else
    check_status 1 "JetBrains 下载服务器连接失败"
fi

echo ""
echo "6. 配置信息总结"
echo "---------------"

echo "工作空间配置:"
echo "  - 工作目录: /data/workspaces"
echo "  - 用户权限: root"
echo "  - JuiceFS 挂载: $(mountpoint -q /data/workspaces && echo '是' || echo '否')"

echo ""
echo "JetBrains Gateway 配置:"
echo "  - 支持的 IDE: WebStorm, RustRover, PyCharm, CLion"
echo "  - 默认 IDE: RustRover"
echo "  - 下载模式: 按需下载"

echo ""
echo "连接信息:"
if [ -n "$CODER_AGENT_TOKEN" ]; then
    echo "  - Agent Token: $(echo $CODER_AGENT_TOKEN | cut -c1-12)..."
    echo "  - Agent 端口: $(echo $CODER_AGENT_TOKEN | tail -c 5)"
fi
echo "  - Coder 服务器: https://$CODER_SERVER"

echo ""
echo "7. 建议操作"
echo "-----------"

if ! pgrep -f "coder.*agent" >/dev/null 2>&1; then
    echo "❗ Coder Agent 未运行，请检查启动脚本或重启工作空间"
fi

if ! mountpoint -q /data/workspaces 2>/dev/null; then
    echo "❗ JuiceFS 未挂载，请检查 JuiceFS 配置或重启工作空间"
fi

if ! curl -s --connect-timeout 5 "https://$CODER_SERVER/healthz" >/dev/null 2>&1; then
    echo "❗ 无法连接到 Coder 服务器，请检查网络连接"
fi

echo ""
echo "✅ 诊断完成！"
echo ""
echo "如果问题仍然存在，请："
echo "1. 在 Coder Web UI 中查看工作空间日志"
echo "2. 检查 JetBrains Gateway 客户端是否正确安装"
echo "3. 确认本地防火墙允许 jetbrains-gateway:// 协议"
echo "4. 联系管理员获取进一步支持"
