#!/bin/bash
set -e

# 系统环境配置脚本

echo "🔧 开始配置系统开发环境..."

# 设置非交互式环境，避免 dpkg-preconfigure 错误
export DEBIAN_FRONTEND=noninteractive

# 更新系统包管理器和环境
echo "📦 更新系统包管理器和环境..."
apt-get update -qq
apt-get upgrade -y -qq

# 安装基础系统工具
echo "🛠️ 安装基础系统工具..."
apt-get install -y -qq \
    ca-certificates \
    gnupg \
    lsb-release

# 安装开发基础工具
echo "🔨 安装开发基础工具..."
apt-get install -y -qq \
    gcc \
    g++ \
    git \
    build-essential \
    pkg-config \
    libssl-dev \
    unzip \
    zip \
    btop \
    tree \
    jq \
    pigz \
    nano

# 安装网络和调试工具
echo "🌐 安装网络和调试工具..."
apt-get install -y -qq \
    net-tools \
    iputils-ping \
    telnet \
    dnsutils \
    tcpdump \
    strace \
    lsof

# 安装 Rust 开发环境 (可选)
echo "🦀 安装 Rust 开发环境..."
apt-get install -y cpanminus
cpanm -q --notest FindBin
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
~/.cargo/bin/cargo install cross
~/.cargo/bin/rustup component add rust-analyzer

# 安装 Node.js 开发环境 (可选)
echo "📦 安装 Node.js 开发环境..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
apt-get install -y nodejs

# 安装 Python 开发环境 (可选)
echo "🐍 安装 Python 开发环境..."
apt-get install -y -qq \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# 安装 Docker (使用官方便捷脚本)
echo "🐳 安装 Docker..."

# 使用Docker官方便捷安装脚本
echo "📥 使用Docker官方安装脚本..."
curl -fsSL https://get.docker.com | sh

# 验证Docker安装
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker安装成功: $(docker --version)"

    # 启动Docker服务
    systemctl enable docker
    systemctl start docker
    echo "✅ Docker服务已启动"

    # 验证Docker运行状态
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker运行正常"
    else
        echo "⚠️  Docker已安装但可能需要重启服务"
    fi
else
    echo "❌ Docker安装失败"
    exit 1
fi



# 清理包管理器缓存
echo "🧹 清理系统缓存..."
apt-get autoremove -y -qq
apt-get autoclean -qq

echo "✅ 系统开发环境配置完成!"
